-- Update memory_visibility_view to explicitly set security_invoker
CREATE OR REPLACE VIEW memory_visibility_view WITH (security_invoker = on) AS
SELECT 
    m.id as memory_id,
    m.creator_id,
    mv.visibility_level,
    mv.allowed_users,
    CASE 
        WHEN mv.visibility_level = 'public' THEN true
        WHEN mv.visibility_level = 'private' THEN false
        WHEN mv.visibility_level = 'friend' THEN EXISTS (
            SELECT 1 FROM user_relationship ur
            WHERE (ur.user_id IN (SELECT id FROM user_profile WHERE id = ur.user_id) AND ur.related_user_id = m.creator_id)
            OR (ur.user_id = m.creator_id AND ur.related_user_id IN (SELECT id FROM user_profile WHERE id = ur.related_user_id))
        )
        WHEN mv.visibility_level = 'close_friend' THEN EXISTS (
            SELECT 1 FROM user_relationship ur
            WHERE (ur.user_id IN (SELECT id FROM user_profile WHERE id = ur.user_id) AND ur.related_user_id = m.creator_id)
            OR (ur.user_id = m.creator_id AND ur.related_user_id IN (SELECT id FROM user_profile WHERE id = ur.related_user_id))
            AND ur.relationship_level = 'close_friend'
        )
        WHEN mv.visibility_level = 'friend_of_friend' THEN EXISTS (
            SELECT 1 FROM user_relationship ur1
            JOIN user_relationship ur2 ON 
                (ur2.user_id = ur1.related_user_id AND ur2.related_user_id = m.creator_id)
                OR (ur2.user_id = m.creator_id AND ur2.related_user_id = ur1.related_user_id)
            WHERE (ur1.user_id IN (SELECT id FROM user_profile WHERE id = ur1.user_id) AND ur1.related_user_id = ur2.user_id)
            OR (ur1.related_user_id IN (SELECT id FROM user_profile WHERE id = ur1.related_user_id) AND ur1.user_id = ur2.user_id)
        )
        ELSE false
    END as is_visible_to_user
FROM memory m
JOIN memory_visibility mv ON mv.memory_id = m.id;

COMMENT ON VIEW memory_visibility_view IS 'View showing memory visibility status for the current user';
