CREATE TABLE public.user_profile (
    id UUID NOT NULL PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    username VARCHAR(1000) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE,
    last_active TIMESTAMP WITH TIME ZONE,
    CONSTRAINT username_length CHECK (char_length(username) >= 5)
);

COMMENT ON TABLE public.user_profile IS 'Stores user profile information linked to auth.users';
COMMENT ON COLUMN public.user_profile.username IS 'Unique username, minimum 5 characters';
COMMENT ON COLUMN public.user_profile.last_login IS 'Timestamp of last authentication';
COMMENT ON COLUMN public.user_profile.last_active IS 'Timestamp of last user activity (updated via API)';

ALTER TABLE public.user_profile ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile." ON user_profile
    FOR SELECT USING ((SELECT auth.uid()) = id);

CREATE POLICY "Users can update their own profile." ON user_profile
    FOR UPDATE USING ((SELECT auth.uid()) = id);

CREATE POLICY "Users can delete their own profile." ON user_profile
    FOR DELETE USING ((SELECT auth.uid()) = id);

-- Function to handle new user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user_profile()
RETURNS trigger
LANGUAGE plpgsql
SECURITY definer SET search_path = ''
AS $$
DECLARE
    v_username TEXT;
BEGIN
    -- Get username from metadata
    v_username := new.raw_user_meta_data ->> 'username';
    
    -- Validate username exists and meets length requirement
    IF v_username IS NULL THEN
        RAISE EXCEPTION 'Username is required in user metadata';
    END IF;
    
    IF char_length(v_username) < 5 THEN
        RAISE EXCEPTION 'Username must be at least 5 characters long';
    END IF;
    
    -- Create profile
    INSERT INTO public.user_profile (id, username)
    VALUES (new.id, v_username);
    
    RETURN new;
EXCEPTION
    WHEN unique_violation THEN
        RAISE EXCEPTION 'Username already exists';
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to create user profile: %', SQLERRM;
END;
$$;

-- Trigger to create profile on user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT on auth.users
    FOR each ROW EXECUTE PROCEDURE public.handle_new_user_profile();

-- Add index for username lookups
CREATE INDEX IF NOT EXISTS idx_user_profile_username ON user_profile(username);

-- Function to update last_login
CREATE OR REPLACE FUNCTION update_last_login()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_login = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update last_login
CREATE TRIGGER update_user_last_login
    BEFORE UPDATE ON user_profile
    FOR EACH ROW
    WHEN (OLD.last_login IS DISTINCT FROM NEW.last_login)
    EXECUTE FUNCTION update_last_login();

-- Function to check username availability
CREATE OR REPLACE FUNCTION is_username_available(p_username TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN NOT EXISTS (
        SELECT 1 FROM user_profile 
        WHERE username = p_username
    );
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION is_username_available IS 'Checks if a username is available for registration';
