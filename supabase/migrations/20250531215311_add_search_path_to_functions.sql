-- Add search_path to all functions
ALTER FUNCTION calculate_expiration_time(memory_duration, timestamptz) SET search_path = public;
ALTER FUNCTION handle_new_memory() SET search_path = public;
ALTER FUNCTION handle_memory_discovery() SET search_path = public;
ALTER FUNCTION handle_memory_visibility() SET search_path = public;
ALTER FUNCTION handle_memory_thread() SET search_path = public;
ALTER FUNCTION can_discover_memory(uuid, uuid) SET search_path = public;
ALTER FUNCTION get_discoverable_memories_batch(uuid, gis.geography, discovery_radius, int, int) SET search_path = public, gis;
ALTER FUNCTION get_memory_thread_batch(uuid, int, int) SET search_path = public;
ALTER FUNCTION get_discoverable_memories_count(uuid, gis.geography, discovery_radius) SET search_path = public, gis;
ALTER FUNCTION get_visible_memories(uuid) SET search_path = public;
ALTER FUNCTION check_memory_visibility(uuid, uuid) SET search_path = public;
ALTER FUNCTION archive_memory(uuid, text, jsonb) SET search_path = public;
ALTER FUNCTION refresh_relationship_connections() SET search_path = public;
ALTER FUNCTION handle_relationship_change() SET search_path = public;
ALTER FUNCTION record_memory_interaction(uuid, uuid, text) SET search_path = public;
ALTER FUNCTION handle_new_user_profile() SET search_path = public;
ALTER FUNCTION update_last_login() SET search_path = public;
ALTER FUNCTION is_username_available(text) SET search_path = public;
ALTER FUNCTION migrate_existing_memories() SET search_path = public;
ALTER FUNCTION check_memory_status() SET search_path = public;
ALTER FUNCTION get_discoverable_memories(uuid, gis.geography, discovery_radius) SET search_path = public, gis;
ALTER FUNCTION update_memory_reply_count() SET search_path = public;
ALTER FUNCTION get_memory_thread(uuid) SET search_path = public;
ALTER FUNCTION update_memory_updated_at() SET search_path = public;
ALTER FUNCTION refresh_memory_discovery_summary() SET search_path = public;

-- Add comments to document the search_path setting
COMMENT ON FUNCTION calculate_expiration_time(memory_duration, timestamptz) IS 'Calculates expiration time based on duration and creation time. Uses public search_path for security.';
COMMENT ON FUNCTION handle_new_memory() IS 'Handles new memory creation, sets expiration and visibility. Uses public search_path for security.';
COMMENT ON FUNCTION handle_memory_discovery() IS 'Handles memory discovery events. Uses public search_path for security.';
COMMENT ON FUNCTION handle_memory_visibility() IS 'Handles memory visibility changes. Uses public search_path for security.';
COMMENT ON FUNCTION handle_memory_thread() IS 'Handles memory thread updates. Uses public search_path for security.';
COMMENT ON FUNCTION can_discover_memory(uuid, uuid) IS 'Checks if a user can discover a memory. Uses public search_path for security.';
COMMENT ON FUNCTION get_discoverable_memories_batch(uuid, gis.geography, discovery_radius, int, int) IS 'Returns a batch of discoverable memories. Uses public and gis search_path for security.';
COMMENT ON FUNCTION get_memory_thread_batch(uuid, int, int) IS 'Returns a batch of memories in a thread. Uses public search_path for security.';
COMMENT ON FUNCTION get_discoverable_memories_count(uuid, gis.geography, discovery_radius) IS 'Returns count of discoverable memories. Uses public and gis search_path for security.';
COMMENT ON FUNCTION get_visible_memories(uuid) IS 'Returns memories visible to a user. Uses public search_path for security.';
COMMENT ON FUNCTION check_memory_visibility(uuid, uuid) IS 'Checks memory visibility for a user. Uses public search_path for security.';
COMMENT ON FUNCTION archive_memory(uuid, text, jsonb) IS 'Archives a memory. Uses public search_path for security.';
COMMENT ON FUNCTION refresh_relationship_connections() IS 'Refreshes relationship connections view. Uses public search_path for security.';
COMMENT ON FUNCTION handle_relationship_change() IS 'Handles relationship changes. Uses public search_path for security.';
COMMENT ON FUNCTION record_memory_interaction(uuid, uuid, text) IS 'Records memory interactions. Uses public search_path for security.';
COMMENT ON FUNCTION handle_new_user_profile() IS 'Handles new user profile creation. Uses public search_path for security.';
COMMENT ON FUNCTION update_last_login() IS 'Updates user last login time. Uses public search_path for security.';
COMMENT ON FUNCTION is_username_available(text) IS 'Checks username availability. Uses public search_path for security.';
COMMENT ON FUNCTION migrate_existing_memories() IS 'Migrates existing memories. Uses public search_path for security.';
COMMENT ON FUNCTION check_memory_status() IS 'Checks memory status for archiving. Uses public search_path for security.';
COMMENT ON FUNCTION get_discoverable_memories(uuid, gis.geography, discovery_radius) IS 'Gets discoverable memories. Uses public and gis search_path for security.';
COMMENT ON FUNCTION update_memory_reply_count() IS 'Updates memory reply count. Uses public search_path for security.';
COMMENT ON FUNCTION get_memory_thread(uuid) IS 'Gets memory thread. Uses public search_path for security.';
COMMENT ON FUNCTION update_memory_updated_at() IS 'Updates memory updated timestamp. Uses public search_path for security.';
COMMENT ON FUNCTION refresh_memory_discovery_summary() IS 'Refreshes memory discovery summary. Uses public search_path for security.';
