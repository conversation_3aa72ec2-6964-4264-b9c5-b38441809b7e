-- Update memory_thread_view to explicitly set security_invoker
CREATE OR REPLACE VIEW memory_thread_view WITH (security_invoker = on) AS
SELECT 
    m.id as memory_id,
    m.creator_id,
    mt.thread_id,
    mt.depth,
    m.reply_count,
    m.created_at,
    up.username as creator_username,
    COALESCE(mds.discovery_count, 0) as discovery_count
FROM memory m
JOIN memory_thread mt ON mt.memory_id = m.id
JOIN user_profile up ON up.id = m.creator_id
LEFT JOIN memory_discovery_summary mds ON mds.memory_id = m.id
WHERE check_memory_visibility(m.id, (SELECT id FROM user_profile WHERE id = m.creator_id));

COMMENT ON VIEW memory_thread_view IS 'View showing thread structure for memories visible to the current user';
