-- Enable RLS on memory_archive table
ALTER TABLE public.memory_archive ENABLE ROW LEVEL SECURITY;

-- Create a policy for selecting rows
CREATE POLICY select_own_memory_archive ON public.memory_archive
FOR SELECT
USING (memory_id IN (
    SELECT id FROM memory 
    WHERE creator_id IN (SELECT id FROM user_profile WHERE id = creator_id)
));

-- Create a policy for inserting rows
CREATE POLICY insert_own_memory_archive ON public.memory_archive
FOR INSERT
WITH CHECK (memory_id IN (
    SELECT id FROM memory 
    WHERE creator_id IN (SELECT id FROM user_profile WHERE id = creator_id)
));

-- Create a policy for updating rows
CREATE POLICY update_own_memory_archive ON public.memory_archive
FOR UPDATE
USING (memory_id IN (
    SELECT id FROM memory 
    WHERE creator_id IN (SELECT id FROM user_profile WHERE id = creator_id)
))
WITH CHECK (memory_id IN (
    SELECT id FROM memory 
    WHERE creator_id IN (SELECT id FROM user_profile WHERE id = creator_id)
));

-- Create a policy for deleting rows
CREATE POLICY delete_own_memory_archive ON public.memory_archive
FOR DELETE
USING (memory_id IN (
    SELECT id FROM memory 
    WHERE creator_id IN (SELECT id FROM user_profile WHERE id = creator_id)
));

COMMENT ON TABLE public.memory_archive IS 'Stores archived memories with reason and metadata';
COMMENT ON POLICY select_own_memory_archive ON public.memory_archive IS 'Users can only view archives of their own memories';
COMMENT ON POLICY insert_own_memory_archive ON public.memory_archive IS 'Users can only archive their own memories';
COMMENT ON POLICY update_own_memory_archive ON public.memory_archive IS 'Users can only update archives of their own memories';
COMMENT ON POLICY delete_own_memory_archive ON public.memory_archive IS 'Users can only delete archives of their own memories';
