-- Enable RLS on memory_interaction table
ALTER TABLE public.memory_interaction ENABLE ROW LEVEL SECURITY;

-- Create a policy for selecting rows
CREATE POLICY select_own_memory_interaction ON public.memory_interaction
FOR SELECT
USING (user_id IN (SELECT id FROM user_profile WHERE id = user_id));

-- Create a policy for inserting rows
CREATE POLICY insert_own_memory_interaction ON public.memory_interaction
FOR INSERT
WITH CHECK (user_id IN (SELECT id FROM user_profile WHERE id = user_id));

-- Create a policy for updating rows
CREATE POLICY update_own_memory_interaction ON public.memory_interaction
FOR UPDATE
USING (user_id IN (SELECT id FROM user_profile WHERE id = user_id))
WITH CHECK (user_id IN (SELECT id FROM user_profile WHERE id = user_id));

-- Create a policy for deleting rows
CREATE POLICY delete_own_memory_interaction ON public.memory_interaction
FOR DELETE
USING (user_id IN (SELECT id FROM user_profile WHERE id = user_id));

COMMENT ON TABLE public.memory_interaction IS 'Tracks user interactions with memories';
COMMENT ON POLICY select_own_memory_interaction ON public.memory_interaction IS 'Users can only view their own memory interactions';
COMMENT ON POLICY insert_own_memory_interaction ON public.memory_interaction IS 'Users can only insert their own memory interactions';
COMMENT ON POLICY update_own_memory_interaction ON public.memory_interaction IS 'Users can only update their own memory interactions';
COMMENT ON POLICY delete_own_memory_interaction ON public.memory_interaction IS 'Users can only delete their own memory interactions';
