-- Function to calculate expiration time
CREATE OR REPLACE FUNCTION calculate_expiration_time(
    p_duration memory_duration,
    p_created_at TIMESTAMP WITH TIME ZONE
)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN CASE p_duration
        WHEN '1d' THEN p_created_at + INTERVAL '1 day'
        WHEN '1w' THEN p_created_at + INTERVAL '1 week'
        WHEN '1m' THEN p_created_at + INTERVAL '1 month'
        ELSE NULL -- forever
    END;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

COMMENT ON FUNCTION calculate_expiration_time IS 'Calculates expiration time based on duration and creation time';

-- Function to handle new memory creation
CREATE OR REPLACE FUNCTION handle_new_memory()
RETURNS TRIGGER AS $$
BEGIN
    -- Set expiration time based on duration
    NEW.expires_at := calculate_expiration_time(NEW.duration, NEW.created_at);
    
    -- Set initial active state
    NEW.is_active := true;
    
    -- Insert into memory_thread
    INSERT INTO memory_thread (memory_id, thread_id, depth)
    VALUES (
        NEW.id,
        CASE 
            WHEN NEW.reply_to_id IS NULL THEN NEW.id
            ELSE NEW.reply_to_id
        END,
        NEW.reply_depth
    );
    
    -- Insert private visibility by default
    INSERT INTO memory_visibility (memory_id, visibility_level)
    VALUES (NEW.id, 'private');
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to create memory: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION handle_new_memory IS 'Handles new memory creation, sets expiration and visibility';

-- Function to handle memory discovery
CREATE OR REPLACE FUNCTION handle_memory_discovery()
RETURNS TRIGGER AS $$
DECLARE
    v_can_discover boolean;
BEGIN
    -- Check if memory can be discovered
    v_can_discover := can_discover_memory(NEW.memory_id, NEW.user_id);
    
    IF NOT v_can_discover THEN
        RAISE EXCEPTION 'Memory cannot be discovered: either discovery limit reached or already discovered by user';
    END IF;

    -- Record the discovery interaction
    PERFORM record_memory_interaction(NEW.memory_id, NEW.user_id, 'discovered');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to handle memory visibility changes
CREATE OR REPLACE FUNCTION handle_memory_visibility()
RETURNS TRIGGER AS $$
BEGIN
    -- If visibility is public, clear allowed_users
    IF NEW.visibility_level = 'public' THEN
        NEW.allowed_users := '{}';
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to handle memory visibility: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Function to handle memory thread updates
CREATE OR REPLACE FUNCTION handle_memory_thread()
RETURNS TRIGGER AS $$
BEGIN
    -- Update thread_id if it's a reply
    IF NEW.reply_to_id IS NOT NULL THEN
        -- Get thread_id from parent
        SELECT thread_id INTO NEW.thread_id
        FROM memory_thread
        WHERE memory_id = NEW.reply_to_id;
        
        -- Set depth
        NEW.depth := NEW.reply_depth;
    ELSE
        -- It's a new thread
        NEW.thread_id := NEW.id;
        NEW.depth := 0;
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to handle memory thread: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Function to check if a user can discover a memory
CREATE OR REPLACE FUNCTION can_discover_memory(
    p_memory_id uuid,
    p_user_id uuid
)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM memory m
        JOIN memory_visibility mv ON mv.memory_id = m.id
        WHERE m.id = p_memory_id
        AND m.is_active = true
        AND m.creator_id != p_user_id
        AND NOT EXISTS (
            SELECT 1 
            FROM memory_discovery md 
            WHERE md.memory_id = m.id 
            AND md.user_id = p_user_id
        )
        AND (
            mv.visibility_level = 'public'
            OR p_user_id = ANY(mv.allowed_users)
            OR EXISTS (
                SELECT 1
                FROM user_relationship ur
                WHERE (
                    (ur.user_id = p_user_id AND ur.related_user_id = m.creator_id)
                    OR (ur.user_id = m.creator_id AND ur.related_user_id = p_user_id)
                )
                AND (
                    (mv.visibility_level = 'friend')
                    OR (mv.visibility_level = 'close_friend' AND ur.relationship_level = 'close_friend')
                )
            )
        )
    );
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION can_discover_memory IS 'Checks if a user can discover a memory based on visibility and relationship status';

-- Function to get discoverable memories in batches
CREATE OR REPLACE FUNCTION get_discoverable_memories_batch(
    p_user_id uuid,
    p_location gis.geography(Point, 4326),
    p_radius discovery_radius DEFAULT '1mi',
    p_batch_size int DEFAULT 20,
    p_offset int DEFAULT 0
)
RETURNS TABLE (
    id uuid,
    creator_id uuid,
    media_type media_type,
    media_url text,
    location gis.geography,
    distance float,
    discovery_count int,
    creator_username text
) AS $$
BEGIN
    RETURN QUERY
    WITH user_radius AS (
        SELECT 
            CASE p_radius
                WHEN '1mi' THEN 1609.34  -- 1 mile in meters
                WHEN '3mi' THEN 4828.02  -- 3 miles in meters
                WHEN '5mi' THEN 8046.72  -- 5 miles in meters
            END as radius_meters
    )
    SELECT 
        m.id,
        m.creator_id,
        m.media_type,
        m.media_url,
        m.location,
        ST_Distance(m.location, p_location) as distance,
        COALESCE(mds.discovery_count, 0) as discovery_count,
        up.username as creator_username
    FROM memory m
    JOIN user_profile up ON up.id = m.creator_id
    LEFT JOIN memory_discovery_summary mds ON mds.memory_id = m.id
    CROSS JOIN user_radius
    WHERE m.is_active = true
    AND m.creator_id != p_user_id
    AND NOT EXISTS (
        SELECT 1 
        FROM memory_discovery md 
        WHERE md.memory_id = m.id 
        AND md.user_id = p_user_id
    )
    AND ST_DWithin(m.location, p_location, radius_meters)
    ORDER BY distance
    LIMIT p_batch_size
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION get_discoverable_memories_batch IS 'Returns a batch of discoverable memories within radius, ordered by distance';

-- Function to get memory thread in batches
CREATE OR REPLACE FUNCTION get_memory_thread_batch(
    p_thread_id uuid,
    p_batch_size int DEFAULT 20,
    p_offset int DEFAULT 0
)
RETURNS TABLE (
    id uuid,
    creator_id uuid,
    media_type media_type,
    media_url text,
    location gis.geography,
    created_at timestamptz,
    reply_depth int,
    reply_count int,
    creator_username text,
    discovery_count int
) AS $$
BEGIN
    -- First check if user can access the root memory
    IF NOT check_memory_visibility(p_thread_id, auth.uid()) THEN
        RETURN;
    END IF;

    RETURN QUERY
    SELECT 
        m.id,
        m.creator_id,
        m.media_type,
        m.media_url,
        m.location,
        m.created_at,
        m.reply_depth,
        m.reply_count,
        up.username as creator_username,
        COALESCE(mds.discovery_count, 0) as discovery_count
    FROM memory m
    JOIN user_profile up ON up.id = m.creator_id
    LEFT JOIN memory_discovery_summary mds ON mds.memory_id = m.id
    WHERE m.id = p_thread_id  -- Root memory
    OR EXISTS (
        SELECT 1 
        FROM memory_thread mt 
        WHERE mt.memory_id = m.id 
        AND mt.thread_id = p_thread_id
    )
    AND check_memory_visibility(m.id, auth.uid())
    ORDER BY m.created_at
    LIMIT p_batch_size
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION get_memory_thread_batch IS 'Returns a batch of memories in a thread, ordered by creation time';

-- Function to get user's discoverable memories count
CREATE OR REPLACE FUNCTION get_discoverable_memories_count(
    p_user_id uuid,
    p_location gis.geography(Point, 4326),
    p_radius discovery_radius DEFAULT '1mi'
)
RETURNS integer AS $$
DECLARE
    v_count integer;
BEGIN
    WITH user_radius AS (
        SELECT 
            CASE p_radius
                WHEN '1mi' THEN 1609.34
                WHEN '3mi' THEN 4828.02
                WHEN '5mi' THEN 8046.72
            END as radius_meters
    )
    SELECT COUNT(*)
    INTO v_count
    FROM memory m
    CROSS JOIN user_radius
    WHERE m.is_active = true
    AND m.creator_id != p_user_id
    AND NOT EXISTS (
        SELECT 1 
        FROM memory_discovery md 
        WHERE md.memory_id = m.id 
        AND md.user_id = p_user_id
    )
    AND ST_DWithin(m.location, p_location, radius_meters)
    AND can_discover_memory(m.id, p_user_id);
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION get_discoverable_memories_count IS 'Returns the total count of discoverable memories for a user';

-- Function to efficiently get visible memories for a user
CREATE OR REPLACE FUNCTION get_visible_memories(p_user_id uuid)
RETURNS TABLE (
    memory_id uuid,
    visibility_level visibility_level,
    creator_id uuid,
    created_at timestamptz
) AS $$
BEGIN
    RETURN QUERY
    WITH user_relationships AS (
        -- Get all relationships (including close friends)
        SELECT DISTINCT
            CASE 
                WHEN ur.user_id = p_user_id THEN ur.related_user_id
                ELSE ur.user_id
            END as related_user_id,
            ur.relationship_level
        FROM user_relationship ur
        WHERE ur.user_id = p_user_id OR ur.related_user_id = p_user_id
    ),
    related_users_of_related_users AS (
        -- Get relationships of relationships
        SELECT DISTINCT
            CASE 
                WHEN ur2.user_id = ur.related_user_id THEN ur2.related_user_id
                ELSE ur2.user_id
            END as indirect_related_user_id
        FROM user_relationships ur
        JOIN user_relationship ur2 ON 
            (ur2.user_id = ur.related_user_id OR ur2.related_user_id = ur.related_user_id)
            AND ur2.user_id != p_user_id 
            AND ur2.related_user_id != p_user_id
            AND (ur2.user_id != ur.related_user_id OR ur2.related_user_id != ur.related_user_id)
    )
    SELECT DISTINCT 
        m.id as memory_id,
        COALESCE(mv.visibility_level, 'private'::visibility_level) as visibility_level,
        m.creator_id,
        m.created_at
    FROM memory m
    LEFT JOIN memory_visibility mv ON mv.memory_id = m.id
    WHERE 
        -- Own memories
        m.creator_id = p_user_id
        OR
        -- Friend visibility
        (COALESCE(mv.visibility_level, 'private'::visibility_level) = 'friend' AND m.creator_id IN (SELECT related_user_id FROM user_relationships))
        OR
        -- Close friend visibility
        (COALESCE(mv.visibility_level, 'private'::visibility_level) = 'close_friend' AND m.creator_id IN (
            SELECT related_user_id FROM user_relationships WHERE relationship_level = 'close_friend'
        ))
        OR
        -- Friend of friend visibility
        (COALESCE(mv.visibility_level, 'private'::visibility_level) = 'friend_of_friend' AND m.creator_id IN (
            SELECT indirect_related_user_id FROM related_users_of_related_users
        ))
    ORDER BY m.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to check memory visibility
CREATE OR REPLACE FUNCTION check_memory_visibility(
    p_memory_id uuid,
    p_user_id uuid
)
RETURNS boolean AS $$
DECLARE
    v_creator_id uuid;
    v_visibility_level visibility_level;
    v_allowed_users uuid[];
    v_excluded_users uuid[];
BEGIN
    -- Get memory creator and visibility in one query
    SELECT m.creator_id, mv.visibility_level, mv.allowed_users, mv.excluded_users
    INTO v_creator_id, v_visibility_level, v_allowed_users, v_excluded_users
    FROM memory m
    LEFT JOIN memory_visibility mv ON mv.memory_id = m.id
    WHERE m.id = p_memory_id;

    -- If memory doesn't exist or no visibility set
    IF v_creator_id IS NULL THEN
        RETURN false;
    END IF;

    -- Creator can always see their own memories
    IF v_creator_id = p_user_id THEN
        RETURN true;
    END IF;

    -- Check if user is explicitly excluded
    IF v_excluded_users @> ARRAY[p_user_id] THEN
        RETURN false;
    END IF;

    -- Check visibility based on level
    RETURN CASE v_visibility_level
        WHEN 'private' THEN false
        WHEN 'public' THEN true
        WHEN 'custom' THEN v_allowed_users @> ARRAY[p_user_id]
        WHEN 'friends' THEN EXISTS (
            SELECT 1
            FROM user_relationship uf
            WHERE (
                (uf.user_id = p_user_id AND uf.related_user_id = v_creator_id)
                OR (uf.user_id = v_creator_id AND uf.related_user_id = p_user_id)
            )
        )
        WHEN 'close_friends' THEN EXISTS (
            SELECT 1
            FROM user_relationship uf
            WHERE (
                (uf.user_id = p_user_id AND uf.related_user_id = v_creator_id)
                OR (uf.user_id = v_creator_id AND uf.related_user_id = p_user_id)
            )
            AND uf.relationship_level = 'close_friend'
        )
        WHEN 'friends_of_friends' THEN EXISTS (
            WITH user_friends AS (
                -- Get direct friends of the user
                SELECT DISTINCT
                    CASE 
                        WHEN uf.user_id = p_user_id THEN uf.related_user_id
                        ELSE uf.user_id
                    END as friend_id
                FROM user_relationship uf
                WHERE uf.user_id = p_user_id OR uf.related_user_id = p_user_id
            )
            -- Check if any of user's friends are friends with the creator
            SELECT 1
            FROM user_friends uf
            JOIN user_relationship uf2 ON 
                (uf2.user_id = uf.related_user_id OR uf2.related_user_id = uf.related_user_id)
                AND (uf2.user_id = v_creator_id OR uf2.related_user_id = v_creator_id)
            LIMIT 1
        )
        ELSE false
    END;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION check_memory_visibility IS 'Checks if a user can view a memory based on visibility and relationship status';

-- Function to archive memory
CREATE OR REPLACE FUNCTION archive_memory(
    p_memory_id uuid,
    p_reason text,
    p_metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS void AS $$
BEGIN
    -- Update memory status
    UPDATE memory
    SET is_active = false,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_memory_id;
    
    -- Insert into archive table
    INSERT INTO memory_archive (memory_id, reason, metadata)
    VALUES (p_memory_id, p_reason, p_metadata);
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION archive_memory IS 'Archives a memory and records the reason';

-- Add materialized view for relationship connections to improve performance
CREATE MATERIALIZED VIEW IF NOT EXISTS relationship_connections AS
WITH relationship_pairs AS (
    SELECT 
        user_id,
        related_user_id,
        relationship_level
    FROM user_relationship
    UNION
    SELECT 
        related_user_id as user_id,
        user_id as related_user_id,
        relationship_level
    FROM user_relationship
)
SELECT 
    f1.user_id,
    f1.related_user_id as direct_related_user_id,
    f2.related_user_id as indirect_related_user_id,
    f1.relationship_level as direct_relationship_level,
    f2.relationship_level as indirect_relationship_level
FROM relationship_pairs f1
JOIN relationship_pairs f2 ON f1.related_user_id = f2.user_id
WHERE f1.user_id != f2.related_user_id;

-- Create indexes on the materialized view
CREATE INDEX IF NOT EXISTS idx_relationship_connections_user ON relationship_connections(user_id);
CREATE INDEX IF NOT EXISTS idx_relationship_connections_direct ON relationship_connections(direct_related_user_id);
CREATE INDEX IF NOT EXISTS idx_relationship_connections_indirect ON relationship_connections(indirect_related_user_id);

-- Function to refresh relationship connections
CREATE OR REPLACE FUNCTION refresh_relationship_connections()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY relationship_connections;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to refresh relationship connections when relationships change
CREATE OR REPLACE FUNCTION handle_relationship_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Schedule a refresh of the materialized view
    PERFORM pg_notify('refresh_relationship_connections', '');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_relationship_change
    AFTER INSERT OR UPDATE OR DELETE ON user_relationship
    FOR EACH ROW
    EXECUTE FUNCTION handle_relationship_change();

-- Function to record memory interaction
CREATE OR REPLACE FUNCTION record_memory_interaction(
    p_memory_id uuid,
    p_user_id uuid,
    p_interaction_type TEXT
)
RETURNS void AS $$
DECLARE
    v_count_updated boolean;
BEGIN
    -- Try to insert a new interaction
    INSERT INTO memory_interaction (memory_id, user_id, interaction_type)
    VALUES (p_memory_id, p_user_id, p_interaction_type)
    ON CONFLICT (memory_id, user_id, interaction_type)
    DO NOTHING
    RETURNING true INTO v_count_updated;
    
    -- Only update count if a new interaction was recorded
    IF v_count_updated THEN
        -- Update the appropriate count on the memory
        IF p_interaction_type = 'opened' THEN
            UPDATE memory
            SET open_count = open_count + 1
            WHERE id = p_memory_id;
        ELSIF p_interaction_type = 'replied' THEN
            UPDATE memory
            SET reply_count = reply_count + 1
            WHERE id = p_memory_id;
        ELSIF p_interaction_type = 'discovered' THEN
            UPDATE memory
            SET discovery_count = discovery_count + 1
            WHERE id = p_memory_id;
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql; 