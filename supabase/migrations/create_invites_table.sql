-- Create invites table
CREATE TABLE IF NOT EXISTS invites (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    phone_number TEXT,
    email TEXT,
    code TEXT NOT NULL,
    invited_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_used BOOLEAN DEFAULT FALSE,
    
    -- Ensure either phone_number or email is provided, but not both
    CONSTRAINT check_contact_info CHECK (
        (phone_number IS NOT NULL AND email IS NULL) OR 
        (phone_number IS NULL AND email IS NOT NULL)
    ),
    
    -- Ensure code is unique
    CONSTRAINT unique_code UNIQUE (code)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_invites_phone_number ON invites(phone_number);
CREATE INDEX IF NOT EXISTS idx_invites_email ON invites(email);
CREATE INDEX IF NOT EXISTS idx_invites_code ON invites(code);
CREATE INDEX IF NOT EXISTS idx_invites_invited_by ON invites(invited_by);
CREATE INDEX IF NOT EXISTS idx_invites_created_at ON invites(created_at);

-- Enable Row Level Security
ALTER TABLE invites ENABLE ROW LEVEL SECURITY;

-- Create policy to allow authenticated users to read their own invites
CREATE POLICY "Users can read their own invites" ON invites
    FOR SELECT USING (auth.uid()::text = invited_by);

-- Create policy to allow authenticated users to insert invites
CREATE POLICY "Users can insert invites" ON invites
    FOR INSERT WITH CHECK (auth.uid()::text = invited_by);

-- Create policy to allow system to read all invites (for verification)
CREATE POLICY "System can read all invites" ON invites
    FOR SELECT USING (true); 