-- Main memory table
CREATE TABLE public.memory (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_id uuid REFERENCES public.user_profile(id) NOT NULL,
    media_type media_type NOT NULL,
    media_url TEXT NOT NULL, -- Supabase Storage URL
    location gis.geography(Point, 4326) NOT NULL, -- PostGIS Point (lat/lng)
    discovery_radius discovery_radius NOT NULL DEFAULT '1mi',
    rarity_type rarity_type NOT NULL,
    rarity_limit INT, -- NULL for unlimited, number for limited
    duration memory_duration NOT NULL DEFAULT 'forever',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    expires_at TIMESTAMP WITH TIME ZONE, -- Calculated based on duration
    last_discovered_at TIMESTAMP WITH TIME ZONE, -- Track last discovery time
    reply_to_id uuid REFERENCES public.memory(id) ON DELETE SET NULL,
    reply_depth INT DEFAULT 0,
    reply_count INT DEFAULT 0,
    discovery_count INT DEFAULT 0, -- Track number of times memory has been discovered
    open_count INT DEFAULT 0, -- Track number of times memory has been opened
    is_active BOOLEAN DEFAULT true -- Track if memory is still discoverable
);

-- New table for memory discovery tracking
CREATE TABLE public.memory_discovery (
    memory_id uuid REFERENCES public.memory(id) ON DELETE CASCADE,
    user_id uuid REFERENCES public.user_profile(id) ON DELETE CASCADE,
    discovered_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    PRIMARY KEY (memory_id, user_id)
);

-- New table for memory interaction tracking
CREATE TABLE public.memory_interaction (
    memory_id uuid REFERENCES public.memory(id) ON DELETE CASCADE,
    user_id uuid REFERENCES public.user_profile(id) ON DELETE CASCADE,
    interaction_type interaction_type NOT NULL,
    interacted_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    PRIMARY KEY (memory_id, user_id, interaction_type)
);

-- New table for simplified visibility
CREATE TABLE public.memory_visibility (
    memory_id uuid REFERENCES public.memory(id) ON DELETE CASCADE,
    visibility_level visibility_level NOT NULL DEFAULT 'private',
    allowed_users uuid[] DEFAULT '{}', -- Array of user IDs who can view
    PRIMARY KEY (memory_id)
);

-- New table for flattened thread structure
CREATE TABLE public.memory_thread (
    memory_id uuid REFERENCES public.memory(id) ON DELETE CASCADE,
    thread_id uuid, -- Root memory ID
    depth int,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    PRIMARY KEY (memory_id)
);

-- New table for memory archive
CREATE TABLE public.memory_archive (
    memory_id uuid PRIMARY KEY REFERENCES public.memory(id) ON DELETE CASCADE,
    reason text NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    archived_at timestamptz DEFAULT now()
);

COMMENT ON TABLE public.memory IS 'Stores user memories with location-based discovery';
COMMENT ON COLUMN public.memory.media_url IS 'URL to the media content in Supabase Storage';
COMMENT ON COLUMN public.memory.location IS 'Geographic location where the memory can be discovered';
COMMENT ON COLUMN public.memory.discovery_radius IS 'Radius within which the memory can be discovered';
COMMENT ON COLUMN public.memory.rarity_limit IS 'Maximum number of times this memory can be discovered (NULL for unlimited)';
COMMENT ON COLUMN public.memory.duration IS 'How long the memory remains active';
COMMENT ON COLUMN public.memory.expires_at IS 'When the memory expires (calculated from duration)';
COMMENT ON COLUMN public.memory.reply_depth IS 'Depth in the reply thread (0 for original memory)';

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_memory_location ON memory USING GIST (location);
CREATE INDEX IF NOT EXISTS idx_memory_creator ON memory(creator_id);
CREATE INDEX IF NOT EXISTS idx_memory_created_at ON memory(created_at);
CREATE INDEX IF NOT EXISTS idx_memory_expires_at ON memory(expires_at);
CREATE INDEX IF NOT EXISTS idx_memory_rarity ON memory(rarity_type, rarity_limit);
CREATE INDEX IF NOT EXISTS idx_memory_reply_to ON memory(reply_to_id);
CREATE INDEX IF NOT EXISTS idx_memory_reply_depth ON memory(reply_depth);
CREATE INDEX IF NOT EXISTS idx_memory_active ON memory(is_active, discovery_radius);
CREATE INDEX IF NOT EXISTS idx_memory_is_active ON memory(is_active);

-- New indexes for better performance
CREATE INDEX IF NOT EXISTS idx_memory_discovery_lookup ON memory_discovery(memory_id, user_id);
CREATE INDEX IF NOT EXISTS idx_memory_discovery_user ON memory_discovery(user_id);
CREATE INDEX IF NOT EXISTS idx_memory_visibility_users ON memory_visibility USING GIN (allowed_users);
CREATE INDEX IF NOT EXISTS idx_memory_thread_lookup ON memory_thread(thread_id, depth);
CREATE INDEX IF NOT EXISTS idx_memory_thread_created ON memory_thread(created_at);

-- Note: For updating the updated_at timestamp, use:
-- UPDATE memory SET updated_at = now() WHERE ...
-- Or include updated_at = now() in your UPDATE statements

-- Function to calculate expiration time
CREATE OR REPLACE FUNCTION calculate_expiration_time(
    p_duration memory_duration,
    p_created_at TIMESTAMP WITH TIME ZONE
)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN CASE p_duration
        WHEN '1d' THEN p_created_at + INTERVAL '1 day'
        WHEN '1w' THEN p_created_at + INTERVAL '1 week'
        WHEN '1m' THEN p_created_at + INTERVAL '1 month'
        ELSE NULL -- forever
    END;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

COMMENT ON FUNCTION calculate_expiration_time IS 'Calculates expiration time based on duration and creation time';

-- Function to handle new memory creation
CREATE OR REPLACE FUNCTION handle_new_memory()
RETURNS TRIGGER AS $$
BEGIN
    -- Set expiration time based on duration
    NEW.expires_at := calculate_expiration_time(NEW.duration, NEW.created_at);
    
    -- Set initial active state
    NEW.is_active := true;
    
    -- Insert into memory_thread
    INSERT INTO memory_thread (memory_id, thread_id, depth)
    VALUES (
        NEW.id,
        CASE 
            WHEN NEW.reply_to_id IS NULL THEN NEW.id
            ELSE NEW.reply_to_id
        END,
        NEW.reply_depth
    );
    
    -- Insert private visibility by default
    INSERT INTO memory_visibility (memory_id, visibility_level)
    VALUES (NEW.id, 'private');
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to create memory: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION handle_new_memory IS 'Handles new memory creation, sets expiration and visibility';

-- Function to handle memory discovery
CREATE OR REPLACE FUNCTION handle_memory_discovery()
RETURNS TRIGGER AS $$
BEGIN
    -- Update last_discovered_at
    UPDATE memory 
    SET last_discovered_at = CURRENT_TIMESTAMP,
        discovery_count = discovery_count + 1
    WHERE id = NEW.memory_id;
    
    -- Refresh discovery summary
    PERFORM refresh_memory_discovery_summary();
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to handle memory discovery: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Function to handle memory visibility changes
CREATE OR REPLACE FUNCTION handle_memory_visibility()
RETURNS TRIGGER AS $$
BEGIN
    -- If visibility is public, clear allowed_users
    IF NEW.visibility_level = 'public' THEN
        NEW.allowed_users := '{}';
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to handle memory visibility: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Function to handle memory thread updates
CREATE OR REPLACE FUNCTION handle_memory_thread()
RETURNS TRIGGER AS $$
BEGIN
    -- Update thread_id if it's a reply
    IF NEW.reply_to_id IS NOT NULL THEN
        -- Get thread_id from parent
        SELECT thread_id INTO NEW.thread_id
        FROM memory_thread
        WHERE memory_id = NEW.reply_to_id;
        
        -- Set depth
        NEW.depth := NEW.reply_depth;
    ELSE
        -- It's a new thread
        NEW.thread_id := NEW.id;
        NEW.depth := 0;
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to handle memory thread: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Triggers
CREATE TRIGGER on_memory_created
    AFTER INSERT ON memory
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_memory();

CREATE TRIGGER on_memory_discovery
    AFTER INSERT ON memory_discovery
    FOR EACH ROW
    EXECUTE FUNCTION handle_memory_discovery();

CREATE TRIGGER on_memory_visibility_change
    BEFORE INSERT OR UPDATE ON memory_visibility
    FOR EACH ROW
    EXECUTE FUNCTION handle_memory_visibility();

CREATE TRIGGER on_memory_thread_change
    BEFORE INSERT OR UPDATE ON memory_thread
    FOR EACH ROW
    EXECUTE FUNCTION handle_memory_thread();

-- Function to migrate existing data
CREATE OR REPLACE FUNCTION migrate_existing_memories()
RETURNS void AS $$
DECLARE
    v_memory record;
BEGIN
    -- Migrate memory_thread
    FOR v_memory IN 
        SELECT id, reply_to_id, reply_depth 
        FROM memory 
        WHERE id NOT IN (SELECT memory_id FROM memory_thread)
    LOOP
        INSERT INTO memory_thread (memory_id, thread_id, depth)
        VALUES (
            v_memory.id,
            CASE 
                WHEN v_memory.reply_to_id IS NULL THEN v_memory.id
                ELSE v_memory.reply_to_id
            END,
            v_memory.reply_depth
        );
    END LOOP;
    
    -- Migrate memory_visibility
    INSERT INTO memory_visibility (memory_id, visibility_level)
    SELECT id, 'private'
    FROM memory
    WHERE id NOT IN (SELECT memory_id FROM memory_visibility);
    
    -- Refresh discovery summary
    PERFORM refresh_memory_discovery_summary();
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION migrate_existing_memories IS 'Migrates existing memories to the new structure';

-- Function to check if memory should be archived
CREATE OR REPLACE FUNCTION check_memory_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if memory should be archived
    IF (
        (NEW.rarity_type = 'limited' AND NEW.discovery_count >= NEW.rarity_limit)
        OR (NEW.expires_at IS NOT NULL AND NEW.expires_at <= now())
    ) THEN
        -- Archive the memory
        PERFORM archive_memory(
            NEW.id,
            CASE 
                WHEN NEW.expires_at <= now() THEN 'expired'
                ELSE 'rarity_limit_reached'
            END,
            jsonb_build_object(
                'expires_at', NEW.expires_at,
                'discovery_count', NEW.discovery_count,
                'rarity_limit', NEW.rarity_limit
            )
        );
        RETURN NULL; -- Prevent the update
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to check memory status
CREATE TRIGGER on_memory_update
    BEFORE UPDATE ON memory
    FOR EACH ROW
    EXECUTE FUNCTION check_memory_status();

-- Function to get discoverable memories within radius
CREATE OR REPLACE FUNCTION get_discoverable_memories(
    p_user_id uuid,
    p_location gis.geography(Point, 4326),
    p_radius discovery_radius DEFAULT '1mi'
)
RETURNS TABLE (
    id uuid,
    creator_id uuid,
    media_type media_type,
    media_url text,
    location gis.geography,
    discovery_radius discovery_radius,
    rarity_type rarity_type,
    rarity_limit int,
    discovery_count int,
    created_at timestamptz,
    distance float
) AS $$
BEGIN
    RETURN QUERY
    WITH user_radius AS (
        SELECT 
            CASE p_radius
                WHEN '1mi' THEN 1609.34  -- 1 mile in meters
                WHEN '3mi' THEN 4828.02  -- 3 miles in meters
                WHEN '5mi' THEN 8046.72  -- 5 miles in meters
            END as radius_meters
    )
    SELECT 
        m.id,
        m.creator_id,
        m.media_type,
        m.media_url,
        m.location,
        m.discovery_radius,
        m.rarity_type,
        m.rarity_limit,
        m.discovery_count,
        m.created_at,
        ST_Distance(m.location, p_location) as distance
    FROM memory m, user_radius
    WHERE m.is_active = true
    AND m.creator_id != p_user_id
    AND NOT EXISTS (
        SELECT 1 
        FROM memory_discovery md 
        WHERE md.memory_id = m.id 
        AND md.user_id = p_user_id
    )
    AND ST_DWithin(m.location, p_location, radius_meters)
    ORDER BY distance;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION get_discoverable_memories IS 'Returns memories that can be discovered by a user within a specified radius';

-- Function to update reply count
CREATE OR REPLACE FUNCTION update_memory_reply_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' AND NEW.reply_to_id IS NOT NULL THEN
        -- Set reply depth based on parent
        SELECT reply_depth + 1 INTO NEW.reply_depth
        FROM memory
        WHERE id = NEW.reply_to_id;
        
        -- Record the reply interaction (this will update reply_count)
        PERFORM record_memory_interaction(NEW.reply_to_id, NEW.creator_id, 'replied');
    ELSIF TG_OP = 'DELETE' AND OLD.reply_to_id IS NOT NULL THEN
        -- Decrease reply count of parent memory
        UPDATE memory 
        SET reply_count = reply_count - 1
        WHERE id = OLD.reply_to_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to maintain reply counts
CREATE TRIGGER on_memory_reply
    AFTER INSERT OR DELETE ON memory
    FOR EACH ROW
    EXECUTE FUNCTION update_memory_reply_count();

-- Enable RLS
ALTER TABLE public.memory ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own memories" ON memory
    FOR SELECT USING (creator_id = auth.uid());

CREATE POLICY "Users can update their own memories" ON memory
    FOR UPDATE USING (creator_id = auth.uid());

CREATE POLICY "Users can delete their own memories" ON memory
    FOR DELETE USING (creator_id = auth.uid());

-- Create optimized indexes for relationship lookups
CREATE INDEX IF NOT EXISTS idx_relationship_user_related ON user_relationship(user_id, related_user_id, relationship_level);
CREATE INDEX IF NOT EXISTS idx_relationship_related_user ON user_relationship(related_user_id, user_id, relationship_level);

-- Function to check memory visibility efficiently
CREATE OR REPLACE FUNCTION check_memory_visibility(
    p_memory_id uuid,
    p_user_id uuid
)
RETURNS boolean AS $$
DECLARE
    v_creator_id uuid;
    v_visibility_level visibility_level;
BEGIN
    -- Get memory creator and visibility in one query
    SELECT m.creator_id, mv.visibility_level 
    INTO v_creator_id, v_visibility_level
    FROM memory m
    LEFT JOIN memory_visibility mv ON mv.memory_id = m.id
    WHERE m.id = p_memory_id;

    -- If memory doesn't exist or no visibility set
    IF v_creator_id IS NULL THEN
        RETURN false;
    END IF;

    -- Creator can always see their own memories
    IF v_creator_id = p_user_id THEN
        RETURN true;
    END IF;

    -- Check visibility based on relationship
    RETURN EXISTS (
        SELECT 1
        FROM user_relationship ur
        WHERE (
            -- Direct relationship check
            (ur.user_id = p_user_id AND ur.related_user_id = v_creator_id)
            OR (ur.user_id = v_creator_id AND ur.related_user_id = p_user_id)
        )
        AND (
            -- Friend visibility
            (v_visibility_level = 'friend')
            OR 
            -- Close friend visibility
            (v_visibility_level = 'close_friend' AND ur.relationship_level = 'close_friend')
            OR
            -- Friend of friend visibility
            (v_visibility_level = 'friend_of_friend' AND EXISTS (
                SELECT 1
                FROM user_relationship ur2
                WHERE (
                    (ur2.user_id = ur.related_user_id AND ur2.related_user_id != p_user_id)
                    OR (ur2.related_user_id = ur.related_user_id AND ur2.user_id != p_user_id)
                )
                AND (
                    (ur2.user_id = v_creator_id)
                    OR (ur2.related_user_id = v_creator_id)
                )
            ))
        )
    );
END;
$$ LANGUAGE plpgsql STABLE;

-- Optimized RLS policy using the new function
CREATE POLICY "Users can view memories shared with them" ON memory
    FOR SELECT USING (
        CASE 
            WHEN reply_to_id IS NOT NULL THEN
                -- For replies, check both reply and parent visibility
                check_memory_visibility(id, auth.uid())
                AND check_memory_visibility(reply_to_id, auth.uid())
            ELSE
                -- For non-replies, just check memory visibility
                check_memory_visibility(id, auth.uid())
        END
    );

-- Optimized thread function
CREATE OR REPLACE FUNCTION get_memory_thread(memory_id uuid)
RETURNS TABLE (
    id uuid,
    creator_id uuid,
    media_type media_type,
    media_url text,
    location gis.geography,
    created_at timestamptz,
    reply_to_id uuid,
    reply_depth int,
    reply_count int,
    creator_username text,
    visibility_level visibility_level
) AS $$
BEGIN
    -- First check if user can access the root memory
    IF NOT check_memory_visibility(memory_id, auth.uid()) THEN
        RETURN;
    END IF;

    RETURN QUERY
    WITH RECURSIVE memory_thread AS (
        -- Base case: the original memory
        SELECT 
            m.*,
            up.username as creator_username,
            mv.visibility_level
        FROM memory m
        JOIN user_profile up ON up.id = m.creator_id
        LEFT JOIN memory_visibility mv ON mv.memory_id = m.id
        WHERE m.id = memory_id
        
        UNION ALL
        
        -- Recursive case: all replies
        SELECT 
            m.*,
            up.username as creator_username,
            mv.visibility_level
        FROM memory m
        JOIN user_profile up ON up.id = m.creator_id
        LEFT JOIN memory_visibility mv ON mv.memory_id = m.id
        JOIN memory_thread mt ON m.reply_to_id = mt.id
        WHERE check_memory_visibility(m.id, auth.uid())
    )
    SELECT 
        id,
        creator_id,
        media_type,
        media_url,
        location,
        created_at,
        reply_to_id,
        reply_depth,
        reply_count,
        creator_username,
        visibility_level
    FROM memory_thread
    ORDER BY created_at;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to update updated_at
CREATE OR REPLACE FUNCTION update_memory_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at
CREATE TRIGGER update_memory_updated_at
    BEFORE UPDATE ON memory
    FOR EACH ROW
    EXECUTE FUNCTION update_memory_updated_at();

-- Function to get discoverable memories in batches
CREATE OR REPLACE FUNCTION get_discoverable_memories_batch(
    p_user_id uuid,
    p_location gis.geography(Point, 4326),
    p_radius discovery_radius DEFAULT '1mi',
    p_batch_size int DEFAULT 20,
    p_offset int DEFAULT 0
)
RETURNS TABLE (
    id uuid,
    creator_id uuid,
    media_type media_type,
    media_url text,
    location gis.geography,
    distance float,
    discovery_count int,
    creator_username text
) AS $$
BEGIN
    RETURN QUERY
    WITH user_radius AS (
        SELECT 
            CASE p_radius
                WHEN '1mi' THEN 1609.34  -- 1 mile in meters
                WHEN '3mi' THEN 4828.02  -- 3 miles in meters
                WHEN '5mi' THEN 8046.72  -- 5 miles in meters
            END as radius_meters
    )
    SELECT 
        m.id,
        m.creator_id,
        m.media_type,
        m.media_url,
        m.location,
        ST_Distance(m.location, p_location) as distance,
        COALESCE(mds.discovery_count, 0) as discovery_count,
        up.username as creator_username
    FROM memory m
    JOIN user_profile up ON up.id = m.creator_id
    LEFT JOIN memory_discovery_summary mds ON mds.memory_id = m.id
    CROSS JOIN user_radius
    WHERE m.is_active = true
    AND m.creator_id != p_user_id
    AND NOT EXISTS (
        SELECT 1 
        FROM memory_discovery md 
        WHERE md.memory_id = m.id 
        AND md.user_id = p_user_id
    )
    AND ST_DWithin(m.location, p_location, radius_meters)
    ORDER BY distance
    LIMIT p_batch_size
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION get_discoverable_memories_batch IS 'Returns a batch of discoverable memories within radius, ordered by distance';

-- Function to get memory thread in batches
CREATE OR REPLACE FUNCTION get_memory_thread_batch(
    p_thread_id uuid,
    p_batch_size int DEFAULT 20,
    p_offset int DEFAULT 0
)
RETURNS TABLE (
    id uuid,
    creator_id uuid,
    media_type media_type,
    media_url text,
    location gis.geography,
    created_at timestamptz,
    reply_depth int,
    reply_count int,
    creator_username text,
    discovery_count int
) AS $$
BEGIN
    -- First check if user can access the root memory
    IF NOT check_memory_visibility(p_thread_id, auth.uid()) THEN
        RETURN;
    END IF;

    RETURN QUERY
    SELECT 
        m.id,
        m.creator_id,
        m.media_type,
        m.media_url,
        m.location,
        m.created_at,
        m.reply_depth,
        m.reply_count,
        up.username as creator_username,
        COALESCE(mds.discovery_count, 0) as discovery_count
    FROM memory m
    JOIN user_profile up ON up.id = m.creator_id
    LEFT JOIN memory_discovery_summary mds ON mds.memory_id = m.id
    WHERE m.id = p_thread_id  -- Root memory
    OR EXISTS (
        SELECT 1 
        FROM memory_thread mt 
        WHERE mt.memory_id = m.id 
        AND mt.thread_id = p_thread_id
    )
    AND check_memory_visibility(m.id, auth.uid())
    ORDER BY m.created_at
    LIMIT p_batch_size
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION get_memory_thread_batch IS 'Returns a batch of memories in a thread, ordered by creation time';

-- Function to refresh discovery summary
CREATE OR REPLACE FUNCTION refresh_memory_discovery_summary()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY memory_discovery_summary;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION refresh_memory_discovery_summary IS 'Refreshes the materialized view of memory discovery summaries';

-- Enable RLS on new tables
ALTER TABLE memory_discovery ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_visibility ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_thread ENABLE ROW LEVEL SECURITY;

-- RLS Policies for memory_discovery
CREATE POLICY "Users can view their own discoveries" ON memory_discovery
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can discover memories" ON memory_discovery
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- RLS Policies for memory_visibility
CREATE POLICY "Users can view visibility of their own memories" ON memory_visibility
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM memory 
            WHERE memory.id = memory_visibility.memory_id 
            AND memory.creator_id = auth.uid()
        )
    );

CREATE POLICY "Users can update visibility of their own memories" ON memory_visibility
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM memory 
            WHERE memory.id = memory_visibility.memory_id 
            AND memory.creator_id = auth.uid()
        )
    );

-- RLS Policies for memory_thread
CREATE POLICY "Users can view threads they have access to" ON memory_thread
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM memory m
            WHERE m.id = memory_thread.memory_id
            AND check_memory_visibility(m.id, auth.uid())
        )
    );

-- Additional indexes for performance
CREATE INDEX IF NOT EXISTS idx_memory_discovery_timestamp ON memory_discovery(discovered_at);
CREATE INDEX IF NOT EXISTS idx_memory_visibility_level ON memory_visibility(visibility_level);
CREATE INDEX IF NOT EXISTS idx_memory_thread_depth ON memory_thread(depth);

-- Constraints
ALTER TABLE memory_thread
    ADD CONSTRAINT fk_memory_thread_root
    FOREIGN KEY (thread_id)
    REFERENCES memory(id)
    ON DELETE CASCADE;

ALTER TABLE memory_visibility
    ADD CONSTRAINT check_visibility_level
    CHECK (visibility_level IN ('private', 'public', 'friend', 'close_friend', 'friend_of_friend'));

-- Function to check if a user can discover a memory
CREATE OR REPLACE FUNCTION can_discover_memory(
    p_memory_id uuid,
    p_user_id uuid
)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM memory m
        JOIN memory_visibility mv ON mv.memory_id = m.id
        WHERE m.id = p_memory_id
        AND m.is_active = true
        AND m.creator_id != p_user_id
        AND NOT EXISTS (
            SELECT 1 
            FROM memory_discovery md 
            WHERE md.memory_id = m.id 
            AND md.user_id = p_user_id
        )
        AND (
            mv.visibility_level = 'public'
            OR p_user_id = ANY(mv.allowed_users)
            OR EXISTS (
                SELECT 1
                FROM user_relationship ur
                WHERE (
                    (ur.user_id = p_user_id AND ur.related_user_id = m.creator_id)
                    OR (ur.user_id = m.creator_id AND ur.related_user_id = p_user_id)
                )
                AND (
                    (mv.visibility_level = 'friend')
                    OR (mv.visibility_level = 'close_friend' AND ur.relationship_level = 'close_friend')
                )
            )
        )
    );
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION can_discover_memory IS 'Checks if a user can discover a memory based on visibility and relationship status';

-- Function to get user's discoverable memories count
CREATE OR REPLACE FUNCTION get_discoverable_memories_count(
    p_user_id uuid,
    p_location gis.geography(Point, 4326),
    p_radius discovery_radius DEFAULT '1mi'
)
RETURNS integer AS $$
DECLARE
    v_count integer;
BEGIN
    WITH user_radius AS (
        SELECT 
            CASE p_radius
                WHEN '1mi' THEN 1609.34
                WHEN '3mi' THEN 4828.02
                WHEN '5mi' THEN 8046.72
            END as radius_meters
    )
    SELECT COUNT(*)
    INTO v_count
    FROM memory m
    CROSS JOIN user_radius
    WHERE m.is_active = true
    AND m.creator_id != p_user_id
    AND NOT EXISTS (
        SELECT 1 
        FROM memory_discovery md 
        WHERE md.memory_id = m.id 
        AND md.user_id = p_user_id
    )
    AND ST_DWithin(m.location, p_location, radius_meters)
    AND can_discover_memory(m.id, p_user_id);
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION get_discoverable_memories_count IS 'Returns the total count of discoverable memories for a user';
