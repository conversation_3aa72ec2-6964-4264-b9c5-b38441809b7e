-- Media types for memories
CREATE TYPE media_type AS ENUM (
    'audio',    -- Cassette tape
    'video',    -- VHS
    'image',    -- Disposable camera
    'text'      -- Postcard/letter
);

-- Visibility levels for memories
CREATE TYPE visibility_level AS ENUM (
    'private',        -- Only creator can see
    'public',         -- Everyone can see
    'friend',         -- Only friends can see
    'close_friend',   -- Only close friends can see
    'friend_of_friend', -- Only friends of friends can see
    'custom'          -- Custom visibility with specific users
);

-- Memory rarity type
CREATE TYPE rarity_type AS ENUM (
    'unlimited',     -- Available to all
    'limited'        -- Limited to X people
);

-- Memory duration type
CREATE TYPE memory_duration AS ENUM (
    '1d',      -- 1 day
    '1w',      -- 1 week
    '1m',      -- 1 month
    'forever'  -- Permanent
);

-- Memory discovery radius type
CREATE TYPE discovery_radius AS ENUM (
    '1mi',     -- 1 mile
    '3mi',     -- 3 miles
    '5mi'      -- 5 miles
);

-- Relationship level type
CREATE TYPE relationship_level AS ENUM (
    'close_friend',  -- Close friend
    'friend',        -- Regular friend
    'acquaintance',  -- Casual acquaintance
    'family'         -- Family member
);

-- Interaction types for memory interactions
CREATE TYPE interaction_type AS ENUM (
    'discovered',  -- User discovered the memory
    'opened',      -- User opened the memory
    'replied'      -- User replied to the memory
);
