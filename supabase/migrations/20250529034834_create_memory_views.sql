-- Create materialized view for memory discovery summary
CREATE MATERIALIZED VIEW memory_discovery_summary AS
SELECT 
    memory_id,
    COUNT(*) as discovery_count,
    COUNT(DISTINCT user_id) as unique_discoverers,
    <PERSON><PERSON>(discovered_at) as first_discovery,
    MA<PERSON>(discovered_at) as last_discovery
FROM memory_discovery
GROUP BY memory_id;

-- <PERSON>reate index on the materialized view
CREATE UNIQUE INDEX idx_memory_discovery_summary_memory_id 
ON memory_discovery_summary(memory_id);

-- Function to refresh the materialized view
CREATE OR REPLACE FUNCTION refresh_memory_discovery_summary()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY memory_discovery_summary;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION refresh_memory_discovery_summary IS 'Refreshes the materialized view of memory discovery summary';

-- Create view for memory visibility
CREATE VIEW memory_visibility_view AS
SELECT 
    m.id as memory_id,
    m.creator_id,
    mv.visibility_level,
    mv.allowed_users,
    CASE 
        WHEN mv.visibility_level = 'public' THEN true
        WHEN mv.visibility_level = 'private' THEN false
        WHEN mv.visibility_level = 'friend' THEN EXISTS (
            SELECT 1 FROM user_relationship ur
            WHERE (ur.user_id = auth.uid() AND ur.related_user_id = m.creator_id)
            OR (ur.user_id = m.creator_id AND ur.related_user_id = auth.uid())
        )
        WHEN mv.visibility_level = 'close_friend' THEN EXISTS (
            SELECT 1 FROM user_relationship ur
            WHERE (ur.user_id = auth.uid() AND ur.related_user_id = m.creator_id)
            OR (ur.user_id = m.creator_id AND ur.related_user_id = auth.uid())
            AND ur.relationship_level = 'close_friend'
        )
        WHEN mv.visibility_level = 'friend_of_friend' THEN EXISTS (
            SELECT 1 FROM user_relationship ur1
            JOIN user_relationship ur2 ON 
                (ur2.user_id = ur1.related_user_id AND ur2.related_user_id = m.creator_id)
                OR (ur2.user_id = m.creator_id AND ur2.related_user_id = ur1.related_user_id)
            WHERE (ur1.user_id = auth.uid() AND ur1.related_user_id = ur2.user_id)
            OR (ur1.related_user_id = auth.uid() AND ur1.user_id = ur2.user_id)
        )
        ELSE false
    END as is_visible_to_user
FROM memory m
JOIN memory_visibility mv ON mv.memory_id = m.id;

COMMENT ON VIEW memory_visibility_view IS 'View showing memory visibility status for the current user';

-- Create view for memory thread structure
CREATE VIEW memory_thread_view AS
SELECT 
    m.id as memory_id,
    m.creator_id,
    mt.thread_id,
    mt.depth,
    m.reply_count,
    m.created_at,
    up.username as creator_username,
    COALESCE(mds.discovery_count, 0) as discovery_count
FROM memory m
JOIN memory_thread mt ON mt.memory_id = m.id
JOIN user_profile up ON up.id = m.creator_id
LEFT JOIN memory_discovery_summary mds ON mds.memory_id = m.id
WHERE check_memory_visibility(m.id, auth.uid());

COMMENT ON VIEW memory_thread_view IS 'View showing thread structure for memories visible to the current user'; 