-- Create user_relationship table
CREATE TABLE public.user_relationship (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES public.user_profile(id) ON DELETE CASCADE,
    related_user_id uuid REFERENCES public.user_profile(id) ON DELETE CASCADE,
    relationship_level relationship_level NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id, related_user_id)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_user_relationship_user_id ON user_relationship(user_id);
CREATE INDEX IF NOT EXISTS idx_user_relationship_related_user_id ON user_relationship(related_user_id);

-- Enable RLS
ALTER TABLE public.user_relationship ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own relationships" ON user_relationship
    FOR SELECT USING (
        user_id = auth.uid() OR related_user_id = auth.uid()
    );

CREATE POLICY "Users can update their own relationships" ON user_relationship
    FOR UPDATE USING (
        user_id = auth.uid() OR related_user_id = auth.uid()
    );

CREATE POLICY "Users can delete their own relationships" ON user_relationship
    FOR DELETE USING (
        user_id = auth.uid() OR related_user_id = auth.uid()
    );

-- Add comments
COMMENT ON TABLE public.user_relationship IS 'Stores relationships between users'; 