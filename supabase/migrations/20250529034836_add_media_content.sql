-- Create memory content table for additional media metadata
CREATE TABLE memory_content (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    memory_id uuid REFERENCES memory(id) ON DELETE CASCADE,
    content_type text NOT NULL,
    content_url text NOT NULL,
    content_size bigint,
    content_metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_memory_content_memory ON memory_content(memory_id);
CREATE INDEX IF NOT EXISTS idx_memory_content_type ON memory_content(content_type);

-- Enable RLS
ALTER TABLE memory_content ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own memory content" ON memory_content
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM memory m
            WHERE m.id = memory_content.memory_id
            AND m.creator_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own memory content" ON memory_content
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM memory m
            WHERE m.id = memory_content.memory_id
            AND m.creator_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own memory content" ON memory_content
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM memory m
            WHERE m.id = memory_content.memory_id
            AND m.creator_id = auth.uid()
        )
    );

-- Function to create a memory with content
CREATE OR REPLACE FUNCTION create_memory_with_content(
    p_creator_id uuid,
    p_location gis.geography(Point, 4326),
    p_content_items jsonb[], -- Array of content items
    p_duration memory_duration DEFAULT 'forever'
)
RETURNS uuid AS $$
DECLARE
    v_memory_id uuid;
    v_content_item jsonb;
    v_order_index int := 0;
BEGIN
    -- Create the memory
    INSERT INTO memory (
        creator_id,
        location,
        duration,
        media_type
    )
    VALUES (
        p_creator_id,
        p_location,
        p_duration,
        CASE
            WHEN array_length(p_content_items, 1) > 1 THEN 'mixed'
            ELSE (p_content_items[1]->>'content_type')::media_type
        END
    )
    RETURNING id INTO v_memory_id;

    -- Insert content items
    FOREACH v_content_item IN ARRAY p_content_items
    LOOP
        INSERT INTO memory_content (
            memory_id,
            content_type,
            content_url,
            text_content,
            order_index,
            metadata
        )
        VALUES (
            v_memory_id,
            (v_content_item->>'content_type')::media_type,
            v_content_item->>'content_url',
            v_content_item->>'text_content',
            v_order_index,
            v_content_item->'metadata'
        );

        v_order_index := v_order_index + 1;
    END LOOP;

    RETURN v_memory_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get memory with all its content
CREATE OR REPLACE FUNCTION get_memory_with_content(
    p_memory_id uuid
)
RETURNS TABLE (
    id uuid,
    creator_id uuid,
    location gis.geography,
    duration memory_duration,
    is_active boolean,
    created_at timestamptz,
    expires_at timestamptz,
    discovery_count int,
    open_count int,
    reply_count int,
    creator_username text,
    content jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.creator_id,
        m.location,
        m.duration,
        m.is_active,
        m.created_at,
        m.expires_at,
        m.discovery_count,
        m.open_count,
        m.reply_count,
        up.username as creator_username,
        (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'id', mc.id,
                    'content_type', mc.content_type,
                    'content_url', mc.content_url,
                    'text_content', mc.text_content,
                    'order_index', mc.order_index,
                    'metadata', mc.metadata
                )
                ORDER BY mc.order_index
            )
            FROM memory_content mc
            WHERE mc.memory_id = m.id
        ) as content
    FROM memory m
    JOIN user_profile up ON up.id = m.creator_id
    WHERE m.id = p_memory_id;
END;
$$ LANGUAGE plpgsql STABLE;

-- Add comments
COMMENT ON TABLE memory_content IS 'Stores different types of content for memories (photo, video, voice, text)';
COMMENT ON FUNCTION create_memory_with_content IS 'Creates a memory with multiple content items';
COMMENT ON FUNCTION get_memory_with_content IS 'Gets a memory with all its content items'; 